// src/components/ui/dispatch-countdown.tsx
'use client';

import React, { useCallback, useEffect, useRef, useState } from 'react';

import { useUnifiedDate } from '@/core/adapters/hooks/useUnifiedDate';
import { AlertTriangle, CheckCircle, Clock, Pause } from 'lucide-react';

import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/shared/components/tooltip';
import { cn } from '@/core/lib/utils';

// 日期时间格式化函数
function formatDateTime(dateString: string): string {
  try {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hour = String(date.getHours()).padStart(2, '0');
    const minute = String(date.getMinutes()).padStart(2, '0');
    return `${year}-${month}-${day} ${hour}:${minute}`;
  } catch {
    return dateString;
  }
}

// src/components/ui/dispatch-countdown.tsx

interface DispatchCountdownProps {
  /** 下次计划发车时间 */
  nextScheduledTime?: string | Date | null;
  /** 上次发车时间 */
  lastDispatchTime?: string | Date | null;
  /** 发车频率（分钟） */
  dispatchFrequencyMinutes?: number;
  /** 发车状态 */
  dispatchStatus?:
  | 'New'
  | 'ReadyToProduce'
  | 'RatioSet'
  | 'InProgress'
  | 'Paused'
  | 'Completed'
  | 'Cancelled';
  /** 自定义样式类名 */
  className?: string;
  /** 紧凑模式 */
  compact?: boolean;
  /** 显示图标 */
  showIcon?: boolean;
}

type CountdownState = 'idle' | 'pending' | 'warning' | 'urgent' | 'overdue' | 'completed';

export const DispatchCountdown: React.FC<DispatchCountdownProps> = ({
  nextScheduledTime,
  lastDispatchTime,
  dispatchFrequencyMinutes,
  dispatchStatus = 'New',
  className,
  compact = false,
  showIcon = true,
}) => {
  // 使用日期适配器
  const dateAdapter = useUnifiedDate(undefined, 'countdown');

  const [timeDisplay, setTimeDisplay] = useState<string>('-');
  const [state, setState] = useState<CountdownState>('idle');
  const [progress, setProgress] = useState<number>(0);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // 格式化时间显示 - 统一转为分钟显示
  const formatTime = useCallback(
    (totalSeconds: number): string => {
      const absSeconds = Math.abs(totalSeconds);
      const totalMinutes = Math.ceil(absSeconds / 60);
      if (totalMinutes > 1440) {
        return '-';
      }
      return `${totalMinutes}m`;
      // if (totalMinutes >= 60) {
      //   const hours = Math.floor(totalMinutes / 60);
      //   const remainingMinutes = totalMinutes % 60;
      //   return compact
      //     ? `${hours}h${remainingMinutes > 0 ? remainingMinutes : ''}m`
      //     : `${hours}小时${remainingMinutes > 0 ? remainingMinutes + '分' : ''}`;
      // } else {
      //   return compact ? `${totalMinutes}m` : `${totalMinutes}分`;
      // }
    },
    [compact]
  );

  // 更新倒计时
  const updateCountdown = useCallback(() => {
    const now = new Date();

    // 如果任务不是进行中状态，不显示倒计时
    if (dispatchStatus !== 'InProgress') {
      setTimeDisplay('-');
      setState('idle');
      setProgress(0);
      return;
    }

    // 如果没有设置发车频率，使用默认值30分钟
    const effectiveFrequency =
      dispatchFrequencyMinutes && dispatchFrequencyMinutes > 0 ? dispatchFrequencyMinutes : 60;

    // 如果没有上次发车时间且没有下次计划时间，显示默认倒计时
    if (!lastDispatchTime && !nextScheduledTime) {
      setTimeDisplay(`${effectiveFrequency}分`);
      setState('pending');
      setProgress(0);
      return;
    }

    // 计算下次发车时间
    let nextTime: Date;
    if (nextScheduledTime) {
      nextTime = new Date(nextScheduledTime);
    } else if (lastDispatchTime) {
      // 基于上次发车时间计算
      const lastTime = new Date(lastDispatchTime);
      nextTime = new Date(lastTime.getTime() + effectiveFrequency * 60 * 1000);
    } else {
      // 基于当前时间计算（新任务）
      nextTime = new Date(now.getTime() + effectiveFrequency * 60 * 1000);
    }

    const totalSecondsDiff = dateAdapter.diff(nextTime, now, 'second');
    const totalFrequencySeconds = effectiveFrequency * 60;

    if (totalSecondsDiff < 0) {
      // 已超时
      const overdueSeconds = Math.abs(totalSecondsDiff);
      setTimeDisplay(`${formatTime(overdueSeconds)}`);
      setState('overdue');
      setProgress(100);
    } else {
      // 倒计时中
      setTimeDisplay(formatTime(totalSecondsDiff));

      // 计算进度
      const elapsedSeconds = totalFrequencySeconds - totalSecondsDiff;
      const progressPercent = Math.min(100, (elapsedSeconds / totalFrequencySeconds) * 100);
      setProgress(progressPercent);

      // 设置状态
      if (totalSecondsDiff <= 60) {
        setState('urgent');
      } else if (totalSecondsDiff <= 300) {
        // 5分钟
        setState('warning');
      } else {
        setState('pending');
      }
    }
  }, [
    nextScheduledTime,
    lastDispatchTime,
    dispatchFrequencyMinutes,
    dispatchStatus,
    formatTime,
    dateAdapter,
  ]);

  useEffect(() => {
    updateCountdown();

    // 设置定时器 - 一分钟更新一次
    const interval = setInterval(updateCountdown, 60000);
    timerRef.current = interval;

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [updateCountdown]);

  // 当关键属性变化时立即更新倒计时
  useEffect(() => {
    updateCountdown();
  }, [nextScheduledTime, lastDispatchTime, dispatchFrequencyMinutes, dispatchStatus]);

  // 获取状态配置
  const getStateConfig = (currentState: CountdownState) => {
    switch (currentState) {
      case 'idle':
        return {
          icon: Pause,
          color: 'text-gray-500',
          bgColor: 'bg-gray-100 dark:bg-gray-800',
          borderColor: 'border-gray-200 dark:border-gray-700',
          progressColor: 'bg-gray-300',
          label: '待定',
        };
      case 'pending':
        return {
          icon: Clock,
          color: 'text-blue-600 dark:text-blue-400',
          bgColor: 'bg-blue-50 dark:bg-blue-900/20',
          borderColor: 'border-blue-200 dark:border-blue-800',
          progressColor: 'bg-blue-500',
          label: '等待中',
        };
      case 'warning':
        return {
          icon: Clock,
          color: 'text-amber-600 dark:text-amber-400',
          bgColor: 'bg-amber-50 dark:bg-amber-900/20',
          borderColor: 'border-amber-200 dark:border-amber-800',
          progressColor: 'bg-amber-500',
          label: '即将发车',
        };
      case 'urgent':
        return {
          icon: AlertTriangle,
          color: 'text-orange-600 dark:text-orange-400',
          bgColor: 'bg-orange-50 dark:bg-orange-900/20',
          borderColor: 'border-orange-200 dark:border-orange-800',
          progressColor: 'bg-orange-500',
          label: '紧急',
        };
      case 'overdue':
        return {
          icon: AlertTriangle,
          color: 'text-red-600 dark:text-red-400',
          bgColor: 'bg-red-50 dark:bg-red-900/20',
          borderColor: 'border-red-200 dark:border-red-800',
          progressColor: 'bg-red-500',
          label: '超时',
        };
      case 'completed':
        return {
          icon: CheckCircle,
          color: 'text-green-600 dark:text-green-400',
          bgColor: 'bg-green-50 dark:bg-green-900/20',
          borderColor: 'border-green-200 dark:border-green-800',
          progressColor: 'bg-green-500',
          label: '已完成',
        };
      default:
        return {
          icon: Clock,
          color: 'text-gray-500',
          bgColor: 'bg-gray-100',
          borderColor: 'border-gray-200',
          progressColor: 'bg-gray-300',
          label: '未知',
        };
    }
  };

  const config = getStateConfig(state);
  const IconComponent = config.icon;

  // 获取工具提示内容
  const getTooltipContent = () => {
    const nextTime = nextScheduledTime
      ? formatDateTime(nextScheduledTime.toString())
      : '未计划';
    const lastTime = lastDispatchTime ? formatDateTime(lastDispatchTime.toString()) : '从未';
    const freq = dispatchFrequencyMinutes ? `${dispatchFrequencyMinutes}分钟` : '未设置';

    return (
      <div className='space-y-2 text-sm'>
        <div className='font-medium text-center'>{config.label}</div>
        <div className='space-y-1 text-xs'>
          <div>
            <span className='text-muted-foreground'>状态:</span> {config.label}
          </div>
          <div>
            <span className='text-muted-foreground'>显示:</span> {timeDisplay}
          </div>
          <div>
            <span className='text-muted-foreground'>下次发车:</span> {nextTime}
          </div>
          <div>
            <span className='text-muted-foreground'>上次发车:</span> {lastTime}
          </div>
          <div>
            <span className='text-muted-foreground'>发车频率:</span> {freq}
          </div>
          {progress > 0 && (
            <div>
              <span className='text-muted-foreground'>进度:</span> {Math.round(progress)}%
            </div>
          )}
        </div>
      </div>
    );
  };

  if (compact) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div
              className={cn(
                'inline-flex items-center justify-center px-1 py-0.5 rounded text-[10px] font-medium transition-colors cursor-help min-w-0',
                config.bgColor,
                config.borderColor,
                config.color,
                className
              )}
            >
              {showIcon && <IconComponent className='w-2 h-2 mr-0.5 flex-shrink-0' />}
              <span className='truncate'>{timeDisplay}</span>
            </div>
          </TooltipTrigger>
          <TooltipContent side='top' className='max-w-xs'>
            {getTooltipContent()}
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div
            className={cn(
              'relative overflow-hidden rounded-lg border transition-all duration-200 cursor-help hover:shadow-md',
              config.bgColor,
              config.borderColor,
              className
            )}
          >
            {/* 进度条背景 */}
            {progress > 0 && (
              <div className='absolute inset-0 opacity-10'>
                <div
                  className={cn('h-full transition-all duration-1000', config.progressColor)}
                  style={{ width: `${progress}%` }}
                />
              </div>
            )}

            {/* 内容 */}
            <div className='relative flex items-center justify-between'>
              <div className='flex items-center gap-2'>
                {showIcon && <IconComponent className={cn('w-4 h-4', config.color)} />}
                <div>
                  <div className={cn('font-medium', config.color)}>{timeDisplay}</div>
                  <div className='text-xs text-muted-foreground'>{config.label}</div>
                </div>
              </div>

              {/* 进度指示器 */}
              {progress > 0 && (
                <div className='text-right'>
                  <div className='text-xs text-muted-foreground'>{Math.round(progress)}%</div>
                  <div className='w-12 h-1 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden'>
                    <div
                      className={cn('h-full transition-all duration-1000', config.progressColor)}
                      style={{ width: `${progress}%` }}
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
        </TooltipTrigger>
        <TooltipContent side='top' className='max-w-xs'>
          {getTooltipContent()}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};
