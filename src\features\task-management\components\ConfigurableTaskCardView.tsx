'use client';

import React, { useCallback, useEffect, useMemo, useState } from 'react';

import { Activity, LayoutGrid, Settings2 } from 'lucide-react';
import { Virtuoso } from 'react-virtuoso';

import { Button } from '@/shared/components/button';
// DragDropContext is now provided at the app level
import { cn } from '@/core/lib/utils';
import type { Task, TaskListStoredSettings, Vehicle, VehicleDisplayMode } from '@/core/types';
import { defaultTaskCardConfig } from '@/core/types/taskCardConfig';

import { CardLayoutConfig, CardLayoutConfigModal } from './cards/CardLayoutConfigModal';
import { ConfigurableTaskCard } from './cards/ConfigurableTaskCard';
import { useCardPerformance } from './cards/useCardPerformance';
import { PerformanceDebugPanel } from './components/performance-debug-panel';

// 默认布局配置
const DEFAULT_LAYOUT_CONFIG = {
  topFields: [
    { id: 'taskNumber', label: '任务编号', visible: true, order: 0 },
    { id: 'constructionSite', label: '施工地点', visible: true, order: 1 },
    { id: 'dispatchStatus', label: '发车状态', visible: true, order: 2 },
    { id: 'customerName', label: '客户名称', visible: true, order: 3 },
  ],
  middleFields: [
    { id: 'requiredVolume', label: '需求方量', visible: true, order: 0 },
    { id: 'completedVolume', label: '完成方量', visible: true, order: 1 },
    { id: 'progress', label: '完成进度', visible: true, order: 2 },
    { id: 'scheduledTime', label: '计划时间', visible: true, order: 3 },
    { id: 'estimatedDuration', label: '预计时长', visible: true, order: 4 },
    { id: 'contactPhone', label: '联系电话', visible: false, order: 5 },
  ],
  showVehicleArea: true,
  cardSize: 'medium' as const,
  spacing: 'normal' as const,
  theme: 'default' as const,
};

interface ConfigurableTaskCardViewProps {
  filteredTasks: Task[];
  vehicles: Vehicle[];
  settings: TaskListStoredSettings;
  vehicleDisplayMode: VehicleDisplayMode;
  onCancelVehicleDispatchAction: (vehicleId: string) => void;
  onOpenStyleEditorAction: () => void;
  onOpenDeliveryOrderDetailsForVehicleAction: (vehicleId: string, taskId: string) => void;
  onOpenVehicleCardContextMenuAction: (e: React.MouseEvent, vehicle: Vehicle, task: Task) => void;
  onTaskContextMenuAction: (e: React.MouseEvent, task: Task) => void;
  onTaskDoubleClickAction: (task: Task) => void;
  onVehicleDispatchedToLineAction?: (vehicleId: string, lineId: string, taskId: string) => void;
  onDropVehicleOnLine?: (vehicle: Vehicle, taskId: string, lineId: string) => void;
}

export const ConfigurableTaskCardView: React.FC<ConfigurableTaskCardViewProps> = ({
  filteredTasks,
  vehicles,
  settings,
  vehicleDisplayMode,
  onCancelVehicleDispatchAction,
  onOpenStyleEditorAction,
  onOpenDeliveryOrderDetailsForVehicleAction,
  onOpenVehicleCardContextMenuAction,
  onTaskContextMenuAction,
  onTaskDoubleClickAction,
  onVehicleDispatchedToLineAction,
  onDropVehicleOnLine,
}) => {
  const [layoutConfig, setLayoutConfig] = useState<CardLayoutConfig>(DEFAULT_LAYOUT_CONFIG);
  const [configModalOpen, setConfigModalOpen] = useState(false);
  const [windowWidth, setWindowWidth] = useState(
    typeof window !== 'undefined' ? window.innerWidth : 1280
  );
  const [debugPanelVisible, setDebugPanelVisible] = useState(false);

  // 性能监控 - 优化虚拟滚动配置
  const { performanceConfig, shouldUseVirtualScroll, renderCountMonitor } = useCardPerformance({
    componentName: 'ConfigurableTaskCardView',
    config: {
      virtualScroll: {
        enabled: true,
        overscan: filteredTasks.length > 100 ? 1 : 2, // 动态调整overscan
        increaseViewportBy: 50, // 减少视口扩展
        itemHeight: 280, // 稍微减少预估高度
      },
    },
    enableMonitoring: process.env.NODE_ENV === 'development', // 只在开发环境启用监控
  });

  // 监控组件渲染
  useEffect(() => {
    renderCountMonitor();
  });

  // 监听窗口大小变化 - 添加防抖优化
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    const handleResize = () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        setWindowWidth(window.innerWidth);
      }, 150); // 防抖延迟150ms
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
      clearTimeout(timeoutId);
    };
  }, []);

  // 计算网格列数
  const columnsPerRow = useMemo(() => {
    const width = windowWidth;

    switch (layoutConfig.cardSize) {
      case 'medium':
        if (width >= 1536) return 5; // 2xl
        if (width >= 1280) return 4; // xl
        if (width >= 1024) return 3; // lg
        if (width >= 768) return 2; // md
        return 1;
      case 'large':
        if (width >= 1536) return 3; // 2xl
        if (width >= 1280) return 2; // xl
        if (width >= 1024) return 2; // lg
        return 1;
      default: // medium
        if (width >= 1536) return 4; // 2xl
        if (width >= 1280) return 3; // xl
        if (width >= 1024) return 2; // lg
        if (width >= 768) return 2; // md
        return 1;
    }
  }, [windowWidth, layoutConfig.cardSize]);

  // 网格样式
  const gridColumns = useMemo(() => {
    return `grid-cols-1 md:grid-cols-${Math.min(columnsPerRow, 2)} lg:grid-cols-${Math.min(columnsPerRow, 3)} xl:grid-cols-${columnsPerRow}`;
  }, [columnsPerRow]);

  // 间距样式
  const spacingClass = useMemo(() => {
    switch (layoutConfig.spacing) {
      case 'tight':
        return 'gap-1 p-1';
      case 'loose':
        return 'gap-3 p-3';
      case 'normal':
      default:
        return 'gap-2 p-2'; // normal
    }
  }, [layoutConfig.spacing]);

  // 缓存车辆分组
  const vehiclesByTask = useMemo(() => {
    const map = new Map<string, Vehicle[]>();

    // 确保 vehicles 是一个有效的数组
    const validVehicles = Array.isArray(vehicles) ? vehicles : [];

    validVehicles.forEach(vehicle => {
      if (vehicle && vehicle.assignedTaskId) {
        if (!map.has(vehicle.assignedTaskId)) {
          map.set(vehicle.assignedTaskId, []);
        }
        map.get(vehicle.assignedTaskId)!.push(vehicle);
      }
    });
    return map;
  }, [vehicles]);

  // 是否使用虚拟滚动
  const useVirtualScroll = shouldUseVirtualScroll(filteredTasks.length);

  // 计算总行数（用于虚拟滚动）
  const totalRows = Math.ceil(filteredTasks.length / columnsPerRow);

  // 渲染行（虚拟滚动）- 优化性能
  const renderRow = useCallback(
    (index: number) => {
      const startIndex = index * columnsPerRow;
      const endIndex = Math.min(startIndex + columnsPerRow, filteredTasks.length);
      const rowTasks = filteredTasks.slice(startIndex, endIndex);

      return (
        <div
          key={`row-${index}`}
          className={cn('grid w-full', gridColumns, spacingClass, 'virtual-row-performance')}
          style={{
            // 添加性能优化样式
            willChange: 'transform',
            transform: 'translateZ(0)',
          }}
        >
          {rowTasks.map(task => {
            const taskVehicles =
              task && task.id && vehiclesByTask instanceof Map
                ? vehiclesByTask.get(task.id) || []
                : [];

            return (
              <ConfigurableTaskCard
                key={task.id}
                task={task}
                vehicles={taskVehicles}
                config={taskCardConfig}
                vehicleDisplayMode={vehicleDisplayMode}
                inTaskVehicleCardStyles={settings.inTaskVehicleCardStyles}
                density={settings.density}
                onCancelDispatch={onCancelVehicleDispatchAction}
                onOpenStyleEditor={onOpenStyleEditorAction}
                onOpenDeliveryOrderDetails={onOpenDeliveryOrderDetailsForVehicleAction}
                onOpenVehicleContextMenu={onOpenVehicleCardContextMenuAction}
                onTaskContextMenu={onTaskContextMenuAction}
                onTaskDoubleClick={onTaskDoubleClickAction}
                onDropVehicleOnLine={onDropVehicleOnLine}
              />
            );
          })}
        </div>
      );
    },
    [
      columnsPerRow,
      filteredTasks,
      gridColumns,
      spacingClass,
      layoutConfig,
      vehiclesByTask,
      vehicleDisplayMode,
      settings.inTaskVehicleCardStyles,
      settings.density,
      onCancelVehicleDispatchAction,
      onOpenStyleEditorAction,
      onOpenDeliveryOrderDetailsForVehicleAction,
      onOpenVehicleCardContextMenuAction,
      onTaskContextMenuAction,
      onTaskDoubleClickAction,
    ]
  );

  // 拖拽处理现在由 DragDropContext 在应用级别处理

  // 保存配置
  const handleSaveConfig = useCallback((config: typeof layoutConfig) => {
    setLayoutConfig(config);
    setConfigModalOpen(false);

    // 这里可以保存到本地存储或发送到服务器
    localStorage.setItem('taskCardLayoutConfig', JSON.stringify(config));
  }, []);

  // 加载保存的配置
  useEffect(() => {
    const savedConfig = localStorage.getItem('taskCardLayoutConfig');
    if (savedConfig) {
      try {
        const parsed = JSON.parse(savedConfig);
        setLayoutConfig({ ...DEFAULT_LAYOUT_CONFIG, ...parsed });
      } catch (error) {
        console.error('Failed to parse saved layout config:', error);
      }
    }
  }, []);

  function getPerformanceReport(): any {
    throw new Error('Function not implemented.');
  }

  return (
    <div className='flex flex-col h-full'>
      {/* 工具栏 */}
      <div className='flex items-center justify-between p-4 border-b bg-background/95 backdrop-blur'>
        <div className='flex items-center gap-4'>
          <div className='flex items-center gap-2'>
            <LayoutGrid className='w-5 h-5 text-muted-foreground' />
            <span className='font-medium'>
              可配置卡片 ({filteredTasks.length})
              {process.env.NODE_ENV === 'development' && (
                <span className='text-xs text-muted-foreground ml-2'>
                  {useVirtualScroll ? '(虚拟滚动)' : '(标准渲染)'}
                </span>
              )}
            </span>
          </div>
        </div>

        <div className='flex items-center gap-2'>
          <Button
            variant='outline'
            size='sm'
            onClick={() => setConfigModalOpen(true)}
            className='flex items-center gap-2'
          >
            <Settings2 className='w-4 h-4' />
            布局配置
          </Button>

          {process.env.NODE_ENV === 'development' && (
            <Button
              variant='outline'
              size='sm'
              onClick={() => {
                const report = getPerformanceReport();
                console.log('Performance Report:', report);
                // 也可以通过日志系统记录
                // performanceLogger.info('Performance report requested', { report });
              }}
              className='flex items-center gap-2'
            >
              <Activity className='w-4 h-4' />
              性能报告
            </Button>
          )}
        </div>
      </div>

      {/* 卡片内容区域 */}
      <div className='flex-1 min-h-0'>
        {useVirtualScroll ? (
          <Virtuoso
            totalCount={totalRows}
            itemContent={renderRow}
            overscan={performanceConfig.virtualScroll.overscan}
            increaseViewportBy={performanceConfig.virtualScroll.increaseViewportBy}
            className='card-grid-performance h-full'
            // 添加性能优化配置
            fixedItemHeight={performanceConfig.virtualScroll.itemHeight}
            scrollSeekConfiguration={{
              enter: velocity => Math.abs(velocity) > 200,
              exit: velocity => Math.abs(velocity) < 30,
            }}
          />
        ) : (
          <div
            className={cn(
              'overflow-auto h-full custom-scrollbar card-grid-performance',
              spacingClass
            )}
          >
            <div className={cn('grid', gridColumns, spacingClass)}>
              {filteredTasks.map(task => {
                const taskVehicles =
                  task && task.id && vehiclesByTask instanceof Map
                    ? vehiclesByTask.get(task.id) || []
                    : [];

                return (
                  <ConfigurableTaskCard
                    key={task.id}
                    task={task}
                    vehicles={taskVehicles}
                    config={defaultTaskCardConfig}
                    vehicleDisplayMode={vehicleDisplayMode}
                    inTaskVehicleCardStyles={settings.inTaskVehicleCardStyles}
                    density={settings.density}
                    onCancelDispatch={onCancelVehicleDispatchAction}
                    onOpenStyleEditor={onOpenStyleEditorAction}
                    onOpenDeliveryOrderDetails={onOpenDeliveryOrderDetailsForVehicleAction}
                    onOpenVehicleContextMenu={onOpenVehicleCardContextMenuAction}
                    onTaskContextMenu={onTaskContextMenuAction}
                    onTaskDoubleClick={onTaskDoubleClickAction}
                    onDropVehicleOnLine={onDropVehicleOnLine}
                  />
                );
              })}
            </div>
          </div>
        )}
      </div>

      {/* 配置弹窗 */}
      <CardLayoutConfigModal
        open={configModalOpen}
        onOpenChangeAction={setConfigModalOpen}
        config={layoutConfig}
        onConfigChangeAction={config => setLayoutConfig(config)}
        onSaveAction={config => handleSaveConfig(config)}
        onCancelAction={() => setConfigModalOpen(false)}
      />

      {/* 性能调试面板 */}
      {process.env.NODE_ENV === 'development' && (
        <PerformanceDebugPanel
          isVisible={debugPanelVisible}
          onToggle={() => setDebugPanelVisible(!debugPanelVisible)}
          componentName='ConfigurableTaskCardView'
          getStats={() => ({
            renderCount: 0, // 这里可以从useCardPerformance获取实际数据
            averageRenderTime: 0,
            lastRenderTime: 0,
          })}
          onReset={() => {
            // 重置性能统计
          }}
        />
      )}
    </div>
  );
};
