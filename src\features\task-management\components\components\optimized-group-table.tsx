import React, { memo } from 'react';

import { ColumnDef, ColumnOrderState, OnChangeFn } from '@tanstack/react-table';

import { VirtualizedTable } from '@/shared/components/virtualized-table';
import { Task } from '@/core/types';
import type { DensityStyleValues } from '@/core/types';

/**
 * 优化的分组表格组件
 * 使用React.memo减少不必要的重新渲染
 */
interface OptimizedGroupTableProps {
  tasks: Task[];
  columns: ColumnDef<Task>[];
  densityStyles: DensityStyleValues;
  enableZebraStriping: boolean;
  estimateRowHeight: (task?: Task) => number;
  totalTableWidth: number;
  columnSizing: Record<string, number>;
  onColumnSizingChange: (updater: any) => void;
  columnVisibility: Record<string, boolean>;
  onColumnVisibilityChange: (updater: any) => void;
  columnOrder: string[];
  onColumnOrderChange: OnChangeFn<ColumnOrderState>;
  onHeaderContextMenu?: (event: React.MouseEvent, columnDef: any) => void;
  onHeaderDoubleClick?: (event: React.MouseEvent, columnDef: any) => void;
  onRowContextMenu?: (event: React.MouseEvent, row: any) => void;
  onRowDoubleClick?: (row: any) => void;
  getColumnBackgroundProps?: (columnId: string) => any;
  getCellTextClasses?: (columnId: string) => string;
  onDropOnProductionLine?: (taskId: string, lineIndex: number) => void;
  isGroupedMode?: boolean; // 新增：是否为分组模式
}

/**
 * 优化的分组表格组件
 * 通过memo和稳定的props减少重新渲染
 */
/**
 * 优化的分组表格组件
 * 通过memo和稳定的props减少重新渲染
 */
export const OptimizedGroupTable = memo(
  ({
    tasks,
    columns,
    densityStyles,
    enableZebraStriping,
    estimateRowHeight,
    totalTableWidth,
    columnSizing,
    onColumnSizingChange,
    columnVisibility,
    onColumnVisibilityChange,
    columnOrder,
    onColumnOrderChange,
    onHeaderContextMenu,
    onHeaderDoubleClick,
    onRowContextMenu,
    onRowDoubleClick,
    getColumnBackgroundProps,
    getCellTextClasses,
    onDropOnProductionLine,
    isGroupedMode = false,
  }: OptimizedGroupTableProps) => {
    // 确保数据有效
    const validTasks = Array.isArray(tasks) ? tasks : [];

    return (
      <VirtualizedTable
        data={validTasks}
        columns={columns}
        getRowId={(row: Task) => row.id}
        densityStyles={densityStyles}
        enableZebraStriping={enableZebraStriping}
        estimateRowHeightAction={(task?: Task) => (task ? estimateRowHeight(task) : 0)}
        totalTableWidth={totalTableWidth}
        columnSizing={columnSizing}
        onColumnSizingChangeAction={onColumnSizingChange}
        columnVisibility={columnVisibility}
        onColumnVisibilityChangeAction={onColumnVisibilityChange}
        columnOrder={columnOrder}
        onColumnOrderChangeAction={onColumnOrderChange}
        onHeaderContextMenuAction={onHeaderContextMenu}
        onHeaderDoubleClickAction={onHeaderDoubleClick}
        onRowContextMenuAction={onRowContextMenu}
        onRowDoubleClickAction={onRowDoubleClick}
        getColumnBackgroundPropsAction={getColumnBackgroundProps}
        getCellTextClassesAction={getCellTextClasses}
        onDropOnProductionLineAction={(vehicle: any, taskId: string, lineId: string) =>
          onDropOnProductionLine?.(taskId, parseInt(lineId))
        }
        isGroupedMode={isGroupedMode}
      />
    );
  },
  (prevProps: OptimizedGroupTableProps, nextProps: OptimizedGroupTableProps) => {
    // 自定义比较函数，只在关键属性变化时重新渲染
    return (
      prevProps.tasks === nextProps.tasks &&
      prevProps.columns === nextProps.columns &&
      prevProps.densityStyles === nextProps.densityStyles &&
      prevProps.enableZebraStriping === nextProps.enableZebraStriping &&
      prevProps.totalTableWidth === nextProps.totalTableWidth &&
      prevProps.columnSizing === nextProps.columnSizing &&
      prevProps.columnVisibility === nextProps.columnVisibility &&
      prevProps.columnOrder === nextProps.columnOrder
    );
  }
);
