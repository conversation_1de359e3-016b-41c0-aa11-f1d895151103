'use client';

import React from 'react';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { GripVertical, Type } from 'lucide-react';

import { Label } from '@/shared/components/label';
import { Switch } from '@/shared/components/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/select';
import { FieldStyle } from '@/core/types/taskCardConfig';

// 字段名称映射（中文化）
const fieldNameMap: Record<string, string> = {
  // 内容区域字段
  requiredVolume: '需求量',
  completedVolume: '已供量',
  scheduledTime: '计划时间',
  contactPhone: '联系电话',
  completedProgress: '完成进度',
  estimatedDuration: '预计时长',
  constructionLocation: '施工位置',
  taskStatus: '任务状态',
  
  // 底部区域字段
  customerName: '客户名称',
  createdAt: '创建时间',
  taskNumber: '任务编号',
  updatedAt: '更新时间',
};

interface SortableFieldItemProps {
  id: string;
  field: FieldStyle;
  onFieldChange: (field: FieldStyle) => void;
  isDisabled?: boolean;
}

const SortableFieldItem: React.FC<SortableFieldItemProps> = ({
  id,
  field,
  onFieldChange,
  isDisabled = false,
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`grid grid-cols-6 gap-2 items-center p-2 border rounded-lg bg-background ${
        isDragging ? 'shadow-lg' : ''
      }`}
    >
      {/* 拖拽手柄 */}
      <div className='col-span-1 flex items-center gap-2'>
        <div
          {...attributes}
          {...listeners}
          className='cursor-grab hover:cursor-grabbing p-1 hover:bg-muted rounded'
        >
          <GripVertical className='w-4 h-4 text-muted-foreground' />
        </div>
        <Label className='text-xs'>{fieldNameMap[id] || id}</Label>
      </div>

      {/* 显示开关 */}
      <div className='col-span-1 flex justify-center'>
        <Switch
          checked={field.visible}
          onCheckedChange={visible => onFieldChange({ ...field, visible })}
          disabled={isDisabled}
        />
      </div>

      {/* 字体大小 */}
      <div className='col-span-1'>
        <Select
          value={field.fontSize}
          onValueChange={fontSize => onFieldChange({ ...field, fontSize: fontSize as any })}
        >
          <SelectTrigger className='h-8 text-xs'>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value='xs'>极小</SelectItem>
            <SelectItem value='sm'>小</SelectItem>
            <SelectItem value='base'>正常</SelectItem>
            <SelectItem value='lg'>大</SelectItem>
            <SelectItem value='xl'>极大</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* 字体粗细 */}
      <div className='col-span-1'>
        <Select
          value={field.fontWeight}
          onValueChange={fontWeight => onFieldChange({ ...field, fontWeight: fontWeight as any })}
        >
          <SelectTrigger className='h-8 text-xs'>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value='normal'>正常</SelectItem>
            <SelectItem value='medium'>中等</SelectItem>
            <SelectItem value='semibold'>半粗</SelectItem>
            <SelectItem value='bold'>粗体</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* 颜色 */}
      <div className='col-span-1'>
        <Select
          value={field.color}
          onValueChange={color => onFieldChange({ ...field, color: color as any })}
        >
          <SelectTrigger className='h-8 text-xs'>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value='default'>默认</SelectItem>
            <SelectItem value='muted'>静音</SelectItem>
            <SelectItem value='primary'>主色</SelectItem>
            <SelectItem value='secondary'>次色</SelectItem>
            <SelectItem value='destructive'>危险</SelectItem>
            <SelectItem value='warning'>警告</SelectItem>
            <SelectItem value='success'>成功</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* 对齐方式 */}
      <div className='col-span-1'>
        <Select
          value={field.textAlign}
          onValueChange={textAlign => onFieldChange({ ...field, textAlign: textAlign as any })}
        >
          <SelectTrigger className='h-8 text-xs'>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value='left'>左对齐</SelectItem>
            <SelectItem value='center'>居中</SelectItem>
            <SelectItem value='right'>右对齐</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  );
};

interface DraggableFieldConfigSectionProps {
  title: string;
  fields: Record<string, FieldStyle>;
  onFieldChange: (fieldKey: string, field: FieldStyle) => void;
  onFieldOrderChange: (newOrder: string[]) => void;
  disabledFields?: string[];
}

export const DraggableFieldConfigSection: React.FC<DraggableFieldConfigSectionProps> = ({
  title,
  fields,
  onFieldChange,
  onFieldOrderChange,
  disabledFields = [],
}) => {
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // 根据order字段排序字段
  const sortedFieldEntries = Object.entries(fields).sort(([, a], [, b]) => {
    const orderA = a.order ?? 999;
    const orderB = b.order ?? 999;
    return orderA - orderB;
  });

  const fieldIds = sortedFieldEntries.map(([key]) => key);

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      const oldIndex = fieldIds.indexOf(active.id as string);
      const newIndex = fieldIds.indexOf(over.id as string);
      
      const newOrder = arrayMove(fieldIds, oldIndex, newIndex);
      onFieldOrderChange(newOrder);
    }
  };

  return (
    <div className='space-y-4'>
      <h4 className='font-medium text-sm flex items-center gap-2'>
        <Type className='w-4 h-4' />
        {title}
      </h4>
      
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragEnd={handleDragEnd}
      >
        <SortableContext items={fieldIds} strategy={verticalListSortingStrategy}>
          <div className='space-y-3'>
            {sortedFieldEntries.map(([key, field]) => {
              const isDisabled = disabledFields.includes(key);
              return (
                <SortableFieldItem
                  key={key}
                  id={key}
                  field={field}
                  onFieldChange={newField => onFieldChange(key, newField)}
                  isDisabled={isDisabled}
                />
              );
            })}
          </div>
        </SortableContext>
      </DndContext>
    </div>
  );
};
